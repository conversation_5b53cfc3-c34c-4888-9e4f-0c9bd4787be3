from typing import Annotated
from ..schemas import InternshipConditionsData, ExemptedSubjectData, StudentStatusDetailData, StudentStatusData, StudentDatas,RecordDatas
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError

router = APIRouter(tags=["srm"])


@router.post("/api/internshipconditions")
def create_internship_condition(
    intern_condition: InternshipConditionsData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            internship_conditions = fastapi.env['th.internship.conditions'].th_create_internship_conditions(datas=intern_condition)
            return {'id': internship_conditions.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

# router tình trạng thực tập
@router.put("/api/internshipconditions/{id}")
def write_internship_condition(
    intern_condition: InternshipConditionsData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            internship_conditions = fastapi.env['th.internship.conditions'].browse(id)
            if not internship_conditions.exists():
                raise HTTPException(status_code=404, detail="Internship Condition not found.")
            data_to_update = intern_condition.model_dump(exclude_unset=True)
            internship_conditions.write(data_to_update)
            return {
                'id': internship_conditions.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/internshipconditions/{id}")
def delete_internship_condition(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            internship_conditions = fastapi.env['th.internship.conditions'].browse(id)
            if not internship_conditions.exists():
                raise HTTPException(status_code=404, detail="Internship Condition not found.")

            internship_conditions.unlink()
            return {"detail": f"Internship Condition with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

# router tình trạng miễn môn


# router tình trạng đăng ký môn
@router.post("/api/subjectregistrationstatus")
def create_subject_registration_status(
    exempted_subject: ExemptedSubjectData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            exempted_Subject = fastapi.env['th.subject.registration.status'].th_create_subject_registration_status(datas=exempted_subject)
            return {'id': exempted_Subject.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/subjectregistrationstatus/{id}")
def write_subject_registration_status(
    subject_registration_status: ExemptedSubjectData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            exempted_Subject = fastapi.env['th.subject.registration.status'].browse(id)
            if not exempted_Subject.exists():
                raise HTTPException(status_code=404, detail="Subject Registration Status not found.")
            data_to_update = subject_registration_status.model_dump(exclude_unset=True)
            exempted_Subject.write(data_to_update)
            return {
                'id': exempted_Subject.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/subjectregistrationstatus/{id}")
def delete_subject_registration_status(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            subject_registration_status = fastapi.env['th.subject.registration.status'].browse(id)
            if not subject_registration_status.exists():
                raise HTTPException(status_code=404, detail="Subject Registration Status not found.")

            subject_registration_status.unlink()
            return {"detail": f"Subject Registration Status with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


#    router tình trạng học tập chi tiết
@router.post("/api/studentstatusdetail")
def create_student_status_detail(
    student_status_detail: StudentStatusDetailData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            student_Status_Detail = fastapi.env['th.student.status.detail'].th_create_student_status_detail(datas=student_status_detail)
            return {'id': student_Status_Detail.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/studentstatusdetail/{id}")
def write_student_status_detail(
    student_status_detail: StudentStatusDetailData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_Status_Detail = fastapi.env['th.student.status.detail'].browse(id)
            if not student_Status_Detail.exists():
                raise HTTPException(status_code=404, detail="Student Status Detail not found.")
            data_to_update = student_status_detail.model_dump(exclude_unset=True)
            student_Status_Detail.write(data_to_update)
            return {
                'id': student_Status_Detail.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/studentstatusdetail/{id}")
def delete_student_status_detail(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_Status_Detail = fastapi.env['th.student.status.detail'].browse(id)
            if not student_Status_Detail.exists():
                raise HTTPException(status_code=404, detail="Student Status Detail Status not found.")

            student_Status_Detail.unlink()
            return {"detail": f"Student Status Detail with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


# router tình trạng học tập
@router.post("/api/studentstatus")
def create_student_status(
    student_status: StudentStatusData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            student_Status = fastapi.env['th.student.status'].th_create_student_status(datas=student_status)
            return {'id': student_Status.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/studentstatus/{id}")
def write_student_status(
    student_status: StudentStatusData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_Status = fastapi.env['th.student.status'].browse(id)
            if not student_Status.exists():
                raise HTTPException(status_code=404, detail="Student Status not found.")
            data_to_update = student_status.model_dump(exclude_unset=True)
            if data_to_update.get('th_status_detail_ids'):
                data_to_update['th_status_detail_ids'] = data_to_update['th_status_detail_ids']
            student_Status.write(data_to_update)
            return {
                'id': student_Status.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/studentstatus/{id}")
def delete_student_status(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_Status = fastapi.env['th.student.status'].browse(id)
            if not student_Status.exists():
                raise HTTPException(status_code=404, detail="Student Status Status not found.")

            student_Status.unlink()
            return {"detail": f"Student Status with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/student")
def create_student(
    student_data:  list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            results = []
            for rec in student_data:
                if rec.id_b2b == 0:
                    values =  rec.model_dump(exclude_none=True, exclude_unset=True)
                    partner_info = values['th_data_srm'].pop('partner_info', None)
                    partner_id = False
                    if not values.get('th_partner_id') and partner_info:
                        # if partner_info['phone']:
                        #     partner_old =  fastapi.env['res.partner'].search([('phone', '=', partner_info['phone'])],limit=1)
                        #     if partner_old:
                        #         values['th_partner_id'] = partner_old.id
                        if partner_info['country_id']:
                            country = fastapi.env['res.country'].search([('code', '=', partner_info['country_id'])],
                                                                        limit=1)
                            partner_info['country_id'] = country.id
                        partner_id = fastapi.env['res.partner'].create(partner_info).id
                        values['th_data_srm']['th_partner_id'] = partner_id if partner_id else False
                        # results.append({
                        #     "th_partner_id": partner.id,
                        # })
                    student = fastapi.env['th.student'].sudo().with_context(th_sync=True).create(values['th_data_srm'])
                    results.append({
                        "status": "success",
                        "response": 'ok',
                        "id": student.id,
                        'th_partner_id': partner_id,
                    })
                elif rec.id_b2b != 0 and rec:
                    values = rec.model_dump(exclude_none=True, exclude_unset=True)
                    response = write_student(values, fastapi, rec.id_b2b)
                    results.append({
                        "status": "success",
                        "response": response,
                        "id": rec.id_b2b,
                        })
                elif rec.id_b2b != 0 and not rec:
                    response = delete_student(fastapi, rec.id_b2b)
                    results.append({
                        "status": "success",
                        "response": response,
                    })
        return results
    except UserError as e:
        raise HTTPException(status_code=400, detail=f"UserError: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


def write_student(
    student_data: StudentDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=1)
):
    try:
        if fastapi:
            student = fastapi.env['th.student'].sudo().with_context(th_sync=True).browse(id)
            if not student.exists():
                raise HTTPException(status_code=404, detail="Student not found.")

            student.write(student_data['th_data_srm'])
            return

    except UserError as e:
        raise HTTPException(status_code=400, detail=f"UserError: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

def delete_student(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=1)
):
    try:
        if fastapi:
            student = fastapi.env['th.student'].sudo().with_context(th_sync=True).browse(id)
            if not student.exists():
                raise HTTPException(status_code=404, detail="Student not found.")
            student.unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=f"UserError: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")