from odoo.models import BaseModel
from odoo.exceptions import ValidationError, UserError
from odoo import fields, models, api, _

class UserAff(BaseModel):
    _inherit = 'res.users'

    def th_create_user(self, datas):
        domain = [('login', '=', datas.login),('active', 'in', [<PERSON>als<PERSON>, True])]
        user = self.sudo().search(domain, limit=1)
        if user:
            if user.active:
                raise ValidationError(_('Tài khoản đã được tạo!'))
            else:
                user.sudo().write({'active':True})
                return user
        if not user:
            user = super(UserAff, self).create(datas.dict())
            user.action_reset_password()
            return user

    # def th_create_user(self, datas):
    #     domain = [('login', '=', datas.login)]
    #     partner = self.sudo().search(domain, limit=1)
    #     if not partner:
    #         partner = super(UserAff, self).create(datas.dict())
    #     return partner