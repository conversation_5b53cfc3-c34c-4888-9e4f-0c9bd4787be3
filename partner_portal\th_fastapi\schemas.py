# Copyright 2022 ACSONE SA/NV
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
import warnings
from enum import Enum
from typing import Annotated, Generic, List, Optional, TypeVar, Union
from datetime import date, datetime
from pydantic import AliasChoices, BaseModel, ConfigDict, Field, computed_field, EmailStr
T = TypeVar("T")


class PagedCollection(BaseModel, Generic[T]):
    count: Annotated[
        int,
        Field(
            ...,
            description="Count of items into the system.\n "
            "Replaces the total field which is deprecated",
            validation_alias=AliasChoices("count", "total"),
        ),
    ]
    items: List[T]

    @computed_field()
    @property
    def total(self) -> int:
        return self.count

    @total.setter
    def total(self, value: int):
        warnings.warn(
            "The total field is deprecated, please use count instead",
            DeprecationWarning,
            stacklevel=2,
        )
        self.count = value


class Paging(BaseModel):
    limit: Optional[int] | None = None
    offset: Optional[int] | None = None


#############################################################
# here above you can find models only used for the demo app #
#############################################################
class DemoUserInfo(BaseModel):
    name: str
    display_name: str


class DemoEndpointAppInfo(BaseModel):
    id: int
    name: str
    app: str
    auth_method: str = Field(alias="demo_auth_method")
    root_path: str
    model_config = ConfigDict(from_attributes=True)


class DemoExceptionType(str, Enum):
    user_error = "UserError"
    validation_error = "ValidationError"
    access_error = "AccessError"
    missing_error = "MissingError"
    http_exception = "HTTPException"
    bare_exception = "BareException"


class User(BaseModel):
    username: str = None
    email: str


class ResponseMessage(BaseModel):
    message: str


class BackLink(BaseModel):
    link_tracker: str
    odoo_utm_params: dict
    referrer: str = None
    code: str = None


class PartnerData(BaseModel):
    name: str
    phone: str


class UserDatas(BaseModel):
    name: str | None = None
    phone: str | None = None
    email: str| None = None
    login: str| None = None
    th_affiliate_code: str | None = None
    th_customer_code: str | None = None
    # th_own_code: str | None = None
    # th_partner_samp: str | None = None
    th_citizen_identification: str | None = None
    # th_date_identification: date | None = None
    th_place_identification: str | None = None
    # th_country: str | None = None
    # th_district: str | None = None
    # th_ward: str | None = None
    # vat: str | None = None


class UserResponse(BaseModel):
    id: int | None = None
    login: Optional[str] | None = None
    email: Optional[str] | None = None
    name: Optional[str] | None = None


class StatusDetailDatas(BaseModel):
    name: Union[bool, str] | None = None
    th_description: Union[bool, str] | None = None
    th_status_category_id: Union[bool, int] | None = None


class StatusCategoryDatas(BaseModel):
    name: Union[bool, str] | None = None
    th_type: Union[bool, str] | None = None
    th_description: Union[bool, str] | None = None
    th_status_detail_ids: Union[bool, list[int]] | None = None


class OwnershipUnitDatas(BaseModel):
    name: Union[bool, str] | None = None
    th_code: Union[bool, str] | None = None
    th_is_sync: Union[bool] | None = None
    th_description: Union[bool, str] | None = None


class SourceGroupDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class GraduationSystemDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class TrainingSystemDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None

class CrmTagDatas(BaseModel):
    name: Union[str, bool] | None = None
    color: Union[int, bool] | None = None

class ProductCategoryDatas(BaseModel):
    name: Union[str, bool] | None = None
    parent_id: Union[int, bool] | None = None
    th_module_ids: Union[list[int], bool] | None = None
    th_origin_ids: Union[list[int], bool] | None = None


class UniversityMajorDatas(BaseModel):
    th_major_code_university: Union[bool, str] | None = None
    th_subject: Union[bool, list[int]] | None = None
    th_major_id: Union[bool, int] | None = None


class OriginDatas(BaseModel):
    name: Union[bool, str] | None = None
    th_code: Union[bool, str] | None = None
    th_module_ids: Union[bool, list[int]] | None = None
    th_university_major_ids: Union[bool, list[UniversityMajorDatas]] | None = None
    th_address: Union[bool, str] | None = None
    th_description: Union[bool, str] | None = None


class MajorDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_major_code_aum: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class AdmissionsStationDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class AdmissionsRegionDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class APMLevelDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_last_status: bool | None = None
    th_first_status: bool | None = None
    th_status_after_sales_care: bool | None = None
    is_after_sale: bool | None = None


class StatusStudentParticularDatas(BaseModel):
    th_status_student_particular: Union[str, bool] | None = None
    th_status: Union[str, bool] | None = None
    th_stop_learning_particular: Union[str, bool] | None = None


class StatusStudentFee(BaseModel):
    th_fee_status_particular: Union[str, bool] | None = None
    th_fee_status: Union[str, bool] | None = None


class ProductTemplateDatas(BaseModel):
    name: Union[str, bool] | None = None
    detailed_type: Union[str, bool] | None = None
    type: Union[str, bool] | None = None
    default_code: Union[str, bool] | None = None
    description: Union[str, bool] | None = None
    list_price: Union[float, bool] | None = None
    sale_ok: bool | None = None
    purchase_ok: bool | None = None
    categ_id: Union[int, bool] | None = None
    th_product_product_samp_ids: Optional[list[int]] | None = None

class InternshipConditionsData(BaseModel):
    name: Union[str, bool] | None = None


class ExemptedSubjectData(BaseModel):
    name: Union[str, bool] | None = None


class SubjectRegistrationStatusData(BaseModel):
    name: Union[str, bool] | None = None


class StudentStatusData(BaseModel):
    name: Union[str, bool] | None = None
    th_status_detail_ids: Union[list[int], bool] | None = None
    note: Union[str, bool] | None = None


class StudentStatusDetailData(BaseModel):
    name: Optional[str] | None = None



class ApmContactTraitDatas(BaseModel):
    th_origin_id: Union[bool, int] | None = None
    th_apm_trait_id: Union[bool, int] | None = None
    th_apm_trait_value_ids: Union[bool, list[int]] | None = None


class ResPartnerDatas(BaseModel):
    th_customer_code: Union[str, bool] | None = None
    name: Union[str, bool] | None = None
    phone: Union[str, bool] | None = None
    th_phone2: Union[str, bool] | None = None
    email: Union[str, bool] | None = None
    th_gender: Union[str, bool] | None = None
    th_birthday: Union[date, bool] | None = None
    th_place_of_birth_id: Union[int, bool] | None = None

    title: Union[int, bool] | None = None
    lang: Union[str, bool] | None = None
    function: Union[str, bool] | None = None
    th_citizen_identification: Union[str, bool] | None = None
    th_date_identification: Union[datetime, bool] | None = None
    th_place_identification: Union[str, bool] | None = None
    vat: Union[str, bool] | None = None

    street: Union[str, bool] | None = None
    th_ward_id: Union[int, bool] | None = None
    th_district_id: Union[int, bool] | None = None
    state_id: Union[int, bool] | None = None
    country_id: Union[str, bool] | None = None

    th_street: Union[str, bool] | None = None
    th_ward_permanent_id: Union[int, bool] | None = None
    th_district_permanent_id: Union[int, bool] | None = None
    th_state_id: Union[int, bool] | None = None
    th_country_id: Union[str, bool] | None = None

    th_ethnicity_id: Union[int, bool] | None = None
    th_religion_id: Union[int, bool] | None = None
    th_module_ids: Union[List[int], bool] | None = None
    th_apm_contact_trait_ids: Union[List[int], bool] | None = None


class CrmLeadDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_ownership_id: Union[int, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_source_name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_major_id: Union[int, bool] | None = None
    type: str | None = None
    th_status_group_id: Union[int, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    stage_id: Union[int, bool] | None = None
    state: Union[str, bool] | None = None
    th_last_check: Union[datetime, bool] | None = None
    th_partner_referred_id: Union[int, bool] | None = None
    th_admissions_station_id: Union[int, bool] | None = None
    th_admissions_region_id: Union[int, bool] | None = None
    th_level_up_date: Union[date, bool] | None = None
    th_graduation_system_id: Union[int, bool] | None = None
    th_utm_source: Union[str, bool] | None = None
    th_utm_medium: Union[str, bool] | None = None
    th_utm_campaign: Union[str, bool] | None = None
    th_utm_term: Union[str, bool] | None = None
    th_utm_content: Union[str, bool] | None = None
    th_crm_job: Union[str, bool] | None = None
    partner_id: Union[int, bool] | None = None
    partner_info: Union[ResPartnerDatas, bool] | None = None
    th_person_in_charge: Union[str, bool] | None = None
    th_self_lead: Union[str, bool] | None = None
    th_duplicate_processed_lead: Union[str, bool] | None = None
    th_is_a_duplicate_opportunity: Union[str, bool] | None = None
    th_duplicate_type: Union[str, bool] | None = None
    th_crm_lead_arbitrate_lose: Union[str, bool] | None = None
    th_dividing_ring_id: Union[int, bool] | None = None
    th_training_system_id: Union[int, bool] | None = None
    tag_ids: Union[List[int], bool] | None = None


class InfoChannelDatas(BaseModel):
    th_channel_samp_id: Union[int, bool] | None = None
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class CountryDistrictDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_ref: Union[int, bool] | None = None
    th_state_id: Union[int, bool] | None = None


class CountryWardDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_ref: Union[int, bool] | None = None
    th_district_id: Union[int, bool] | None = None


class CountryStateDatas(BaseModel):
    name: Union[str, bool] | None = None
    code: Union[str, bool] | None = None
    country_id: Union[str, bool] | None = None


class StudentProfileDatas(BaseModel):
    th_lead_id: Union[int, bool] | None = None
    th_date_of_delivery: Union[date, bool] | None = None
    th_handover_status: Union[str, bool] | None = None
    th_date_of_receipt: Union[date, bool] | None = None
    th_profile_status: Union[str, bool] | None = None
    th_calling_status_id: Union[str, bool] | None = None
    th_reason: Union[str, bool] | None = None
    th_cover_profile: Union[str, bool] | None = None
    th_profile_image: Union[str, bool] | None = None
    th_profile_degree: Union[str, bool] | None = None
    th_profile_graduate: Union[str, bool] | None = None
    th_profile_transcript: Union[str, bool] | None = None
    th_school_profile: Union[str, bool] | None = None
    th_pdk: Union[str, bool] | None = None
    # th_test_paper: Union[str, bool] | None = None
    th_citizen_identification: Union[str, bool] | None = None
    # th_curriculum_vitae: Union[str, bool]
    th_score_transfer: Union[str, bool] | None = None
    th_other: Union[str, bool] | None = None
    th_job: Union[str, bool] | None = None
    th_unit_for_work: Union[str, bool] | None = None
    th_awarding_diplomas: Union[str, bool] | None = None
    th_high_school: Union[str, bool] | None = None
    th_major_studied: Union[str, bool] | None = None
    th_graduation_rank: Union[str, bool] | None = None
    th_graduation_year: Union[str, bool] | None = None
    th_form_of_train: Union[str, bool] | None = None
    th_certificate_number: Union[str, bool] | None = None
    th_reference_number: Union[str, bool] | None = None
    th_subject_score_1: Union[float, bool] | None = None
    th_subject_score_2: Union[float, bool] | None = None
    th_subject_score_3: Union[float, bool] | None = None
    th_medium_score_in4: Union[float, bool] | None = None
    th_medium_score_in10: Union[float, bool] | None = None
    th_12_performance: Union[str, bool] | None = None
    th_12_conduct: Union[str, bool] | None = None
    th_father_name: Union[str, bool] | None = None
    th_father_job: Union[str, bool] | None = None
    th_father_birther: Union[str, bool] | None = None
    th_mother_name: Union[str, bool] | None = None
    th_mother_job: Union[str, bool] | None = None
    th_mother_birther: Union[str, bool] | None = None


class MailMessageDatas(BaseModel):
    date: Union[datetime, bool] | None = None
    author_id: Union[int, bool] | None = None
    message_type: Union[str, bool] | None = None
    model: Union[str, bool] | None = None
    res_id: Union[int, bool] | None = None
    body: Union[str, bool] | None = None


class DividingRingDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_is_partner_dividing: bool | None = None


class DuplicateCheckHistoryDatas(BaseModel):
    th_name_lead_lose: Union[str, bool] | None = None
    th_ownership_id: Union[int, bool] | None = None
    th_status_group_id: Union[int, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_stage_id: Union[int, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_major_id: Union[int, bool] | None = None
    th_registration_date: Union[date, bool] | None = None
    th_last_check: Union[datetime, bool] | None = None
    th_lead_id_old: Union[int, bool] | None = None
    th_lead_id_new: Union[int, bool] | None = None
    th_graduation_system_id: Union[int, bool] | None = None
    th_admissions_station_id: Union[int, bool] | None = None
    th_admissions_region_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_dividing_ring_id: Union[int, bool] | None = None
    user_id: Union[int, bool] | None = None
    th_source_name: Union[str, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    # th_form_name: Union[str, bool]
    # th_uuid_form: Union[str, bool]
    th_partner_referred_id: Union[int, bool] | None = None
    th_duplicate_type: Union[str, bool] | None = None
    state: Union[str, bool] | None = None
    # th_self_lead: bool | None = None


class CrmStageDatas(BaseModel):
    name: Union[str, bool] | None = None
    is_won: bool | None = None
    requirements: Union[str, bool] | None = None
    th_type: Union[str, bool] | None = None
    th_auto: bool | None = None
    th_required_fill: bool | None = None


class ApmCampaignDatas(BaseModel):
    name: Union[str, bool] | None = None
    state: Union[str, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_divide: Union[str, bool] | None = None
    is_campaign_auto: bool | None = None
    th_apm_trait_ids: Union[list[int], bool] | None = None
    th_apm_trait_value_ids: Union[list[int], bool] | None = None



class APMLeadDatas(BaseModel):
    # name: Union[str, bool] | None = None

    th_ownership_unit_id: Union[int, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_status_category_id: Union[int, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    th_stage_id: Union[int, bool] | None = None
    th_last_check: Union[datetime, bool] | None = None
    th_partner_reference_id: Union[int, bool] | None = None
    th_level_up_date: Union[date, bool] | None = None
    th_partner_id: Union[int, bool] | None = None
    th_campaign_id: Union[int, bool] | None = None
    # th_product_category_ids: Union[list[int], bool]
    partner_info: Optional[ResPartnerDatas] | None = None
    th_apm_contact_trait_ids: Union[List[int], bool] | None = None
    th_product_ids: Union[list[int], bool] | None = None
    th_apm_team_id: Union[int, bool] | None = None


class ApmTraitValueDatas(BaseModel):
    name: Union[str, bool] | None = None

class ApmTraitDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_origin_id: Union[bool, int] | None = None
    th_apm_trait_value_ids: Union[bool, list[int]] | None = None


class StudentDatas(BaseModel):
    th_semester: Union[str, bool] | None = None
    name: Union[str, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_major_id: Union[int, bool] | None = None
    th_class: Union[str, bool] | None = None
    th_specialization_class: Union[str, bool] | None = None
    th_user_id: Union[str, bool] | None = None
    th_student_code: Union[str, bool] | None = None
    th_status: Union[str, bool] | None = None
    th_student_status_particular: Union[int, bool] | None = None
    th_status_category_id: Union[int, bool] | None = None
    th_partner_id: Union[int, bool] | None = None
    th_group: Union[str, bool] | None = None
    th_fee_status: Union[str, bool] | None = None
    th_fee_status_particular: Union[int, bool] | None = None
    th_paid_address: Union[str, bool] | None = None
    th_owner_id: Union[int, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    th_admission_num: Union[str, bool] | None = None
    th_acceptance: Union[str, bool] | None = None
    th_exempted_sub: Union[str, bool] | None = None
    th_exempted_sub_detail_id: Union[int, bool] | None = None
    th_internship_detail_id: Union[int, bool] | None = None
    th_learning_status_id: Union[int, bool] | None = None
    th_next_check: Union[date, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_subject_registration_status: Union[str, bool] | None = None
    th_graduation_system_id: Union[int, bool] | None = None
    th_stop_learning_particular: Union[str, bool] | None = None
    th_reason_stop_learning: Union[str, bool] | None = None
    th_stop_learning_date: Union[date, bool] | None = None
    th_description: Union[str, bool] | None = None
    partner_info: Optional[ResPartnerDatas] | None = None


class ResPartnerTitleDatas(BaseModel):
    name: Union[str, bool] | None = None
    shortcut: Union[str, bool] | None = None
class ApmTeamDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None


class OrderLine(BaseModel):
    product_id: Union[int, bool] | None = None
    product_template_id: Union[int, bool] | None = None
    product_uom_qty: Union[float, bool] | None = None
    discount: Union[float, bool] | None = None
    price_unit: Union[float, bool] | None = None


class SaleOrderDatas(BaseModel):
    name: Union[str, bool] | None = None
    partner_id: Union[int, bool] | None = None
    th_apm_source_group_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_total_received_excessive: Union[float, bool] | None = None
    date_order: Union[datetime, bool] | None = None
    pricelist_id: Union[int, bool] | None = None
    payment_term_id: Union[int, bool] | None = None
    th_introducer_id: Union[int, bool] | None = None
    partner_invoice_id: Union[int, bool] | None = None
    partner_shipping_id: Union[int, bool] | None = None
    th_status: Union[str, bool] | None = None
    state: Union[str, bool] | None = None
    th_apm_id: Union[int, bool] | None = None
    order_lines: Union[list[OrderLine], bool] | None = None
    th_srm_major: Union[int, bool] | None = None
    th_identifier_code: Union[str, bool] | None = None
    th_srm_specialization_class: Union[str, bool] | None = None
    th_srm_class: Union[str, bool] | None = None
    th_partner_birthday: Union[date, bool] | None = None
    th_srm_lead_id: Union[int, bool] | None = None
    th_srm_school: Union[int, bool] | None = None
    th_sale_order: Union[str, bool] | None = None
    th_status_payment_invoiced: Union[str, bool] | None = None


class PricelistItemDatas(BaseModel):
    pricelist_id: Union[int, bool] | None = None
    compute_price: str | None = None
    date_start: Union[datetime, bool] | None = None
    date_end: Union[datetime, bool] | None = None
    min_quantity: float | None = None
    applied_on: str | None = None
    categ_id: Union[int, bool] | None = None
    product_tmpl_id: Union[int, bool] | None = None

    fixed_price: Optional[float] | None = None
    percent_price: Optional[float] | None = None
    base: Optional[str] | None = None
    price_discount: Optional[float] | None = None
    price_surcharge: Optional[float] | None = None
    price_round: Optional[float] | None = None
    price_min_margin: Optional[float] | None = None
    price_max_margin: Optional[float] | None = None


class PricelistDatas(BaseModel):
    name: Union[str, bool] | None = None
    items: list[PricelistItemDatas] | None = None


class RecordDatas(BaseModel):
    id_b2b: Union[int, bool] | None = None
    th_data_res_partner: ResPartnerDatas | None = None
    th_data_crm: CrmLeadDatas | None = None
    th_data_apm: APMLeadDatas | None = None
    th_data_mail: MailMessageDatas | None = None

    th_data_apm_trait: ApmTraitDatas | None = None
    # th_data_sale: SaleOrderDatas | None = None
    th_data_srm: StudentDatas | None = None


class CreateRecordDatas(BaseModel):
    th_data_sale: SaleOrderDatas | None = None
    id_b2b: Union[int, bool] | None = None

