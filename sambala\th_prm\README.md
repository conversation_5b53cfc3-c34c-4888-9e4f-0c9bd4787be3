# Welcome to PRM Module!

# `YÊU CẦU NGHIỆP VỤ MODULE PRM`

# **I. GIỚI THIỆU CHUNG**

1. Module PRM có 2 tính năng chính, bao gồm:

* PRM (Partner Relationship Management) dùng để quản lý quan hệ đối tác. <PERSON><PERSON>i tác của PRM là những đối tượng khách hàng
  có thể là cá nhân hoặc tổ chức mà có tiềm năng, c<PERSON> hội trở thành đối tác của người sử dụng (cá nhân hoặc tổ chức người
  sử dụng) trong tương lai.
* POM (Partner Operating Management) dùng để quản lý điều hành đối tác. Đối tác của POM là những đối tượng khách hàng
  PRM sau khi đã trở thành đối tác của người sử dụng.

---

2. <PERSON><PERSON> quyền cho các nhóm người dùng

* <PERSON><PERSON><PERSON> tính năng trong module PRM được phân quyền cho 4 nhóm người dùng chính: <PERSON><PERSON><PERSON> vi<PERSON>, Quản lý, Trưởng phòng và Quản
  trị viên.
* Mỗi một nhóm người dùng sẽ có các quyền nhất định về xem, tạo, sửa, xóa các bản ghi cùng các trường thông tin ở các
  tính năng trong module.

# **II. CẤU HÌNH**

Để các tính năng của module hoạt động trơn tru, hiệu quả thì điều kiện tiên quyết là phải thực hiện cấu hình (chỉ Quản
trị viên mới có quyền cấu hình).

1. Mối quan hệ

* Tên: tên của mối quan hệ, thể hiện mối quan hệ giữa người sử dụng với các đối tác, sẽ hiển thị ở thanh trạng thái
  trong các bản ghi của PRM hay POM.
* Loại: phân loại mối quan hệ đấy là của PRM hay POM.
* Trạng thái đầu: để chỉ mối quan hệ đấy là trạng thái đầu tiên trong các trạng thái mối quan hệ khi chăm sóc các đối
  tác.
* Trạng thái cuối: để chỉ mối quan hệ đấy là trạng thái cuối cùng trong các trạng thái mối quan hệ khi chăm sóc các đối
  tác.
* Cần thông tin và giấy tờ: để chỉ định khi chuyển sang mối quan hệ đấy bắt buộc cần phải có thông tin và giấy tờ.
* Mô tả: ghi chú hoặc mô tả thêm về mối quan hệ khi tạo.

---

2. Nhóm đối tác

* Tên: tên của nhóm đối tác, có thể là tên công ty khi có nhiều đối tác thuộc công ty đó hoặc tên khu vực khi có nhiều
  đối tác cùng thuộc khu vực đó...
* Mô tả: ghi chú hoặc mô tả thêm về nhóm đối tác khi tạo.

---

3. Nguồn đối tác

* Tên: tên của nguồn đối tác, là nguồn gốc mà có được mối quan hệ với đối tác được cấu hình, có thể là do được giới
  thiệu hoặc tìm kiếm hay qua các trạng mạng xã hội cùng các kênh truyền thông phương tiện...
* Mô tả: ghi chú hoặc mô tả thêm về nguồn đối tác khi tạo.

---

4. Sản phẩm hợp tác

* Tên: tên của sản phẩm mà bên người sử dụng hợp tác phát triển hoặc triển khai cùng với đối tác.
* Mô tả: ghi chú hoặc mô tả thêm về sản phẩm hợp tác khi tạo.

---

5. Chính sách hợp tác

* Tên: tên của chính sách hợp tác, là chính sách mà bên người sử dụng hợp tác với đối tác, ví dụ như hợp tác theo chính
  sách 1 đổi 1 hoặc trả hàng trong vòng 7 ngày với các sản phẩm hợp tác là hàng tiêu dùng...
* Mô tả: ghi chú hoặc mô tả thêm về chính sách hợp tác khi tạo.

---

6. Lý do ngừng hợp tác

* Loại: tên của nhóm lý do ngừng hợp tác.
* Mỗi một loại lý do ngừng hợp tác sẽ có một bảng chi tiết riêng để cấu hình các lý do ngừng hợp tác đối với từng loại
  lý do ngừng hợp tác.
* Tên: tên chi tiết lý do người sử dụng ngừng hợp tác với các đối tác (ví dụ như không thanh toán hoặc không có nhu
  cầu).
* Mô tả: ghi chú hoặc mô tả thêm về chi tiết lý do ngừng hợp tác khi tạo.

---

7. Tình trạng gọi

* Nhóm trạng thái: thể hiện nhóm trạng thái chung mà người sử dụng khi gọi cho các đối tác.
* Mối quan hệ: các mối quan hệ (cấu hình ở tính năng Mối quan hệ) muốn gắn liền với trạng thái gọi.
* Mô tả: ghi chú hoặc mô tả thêm về nhóm trạng thái khi tạo.
* Mỗi một nhóm trạng thái sẽ có một bảng trạng thái chi tiết riêng để cấu hình các trạng thái cụ thể đối với từng loại
  nhóm trạng thái.
* Tên trạng thái: tên trạng thái cụ thể khi gọi (ví dụ như máy bận hoặc không nghe máy).
* Mô tả: ghi chú hoặc mô tả thêm về tên trạng thái khi tạo.
* Mối quan hệ: các mối quan hệ (được chọn ở bên trên khi cấu hình nhóm trạng thái) muốn gắn liền với trạng thái gọi cụ
  thể.

---

8. Đội nhóm

* Tên đội: tên đội nhóm của người sử dụng khi chăm sóc các đối tác.
* Đội/nhóm cha: được chọn một trong các đội nhóm đã tạo trước đó (nếu có) làm đội nhóm cha.
* Đơn vị sở hữu: nếu có Đội/nhóm cha sẽ mặc định là đơn vị sở hữu của Đội/nhóm cha, nếu không thì được chọn một trong
  các đơn vị sở hữu (cấu hình trong chức năng Cài đặt thông số).
* Quản lý: sẽ hiện nếu người dùng là Quản lý, chọn hoặc tạo một trong các người dùng làm người quản lý đội nhóm (cấu
  hình trong chức năng Người dùng).
* Mỗi một đội nhóm sẽ có một bảng thành viên riêng để cấu hình các thành viên cụ thể cho từng đội nhóm.
* Nút thêm: thêm một hoặc nhiều người dùng để làm thành viên của đội nhóm.
* Nếu thành viên không có Đội/nhóm cha thì sẽ hiển thị toàn bộ nhân viên.
* Nếu thành viên có Đội/nhóm cha thì sẽ chỉ hiển thị các thành viên của đội nhóm cha đấy.

Sau khi thực hiện cấu hình, các tính năng trong module PRM và POM hoàn toàn đã có thể sử dụng.

# **III. PRM (PARTNER RELATIONSHIP MANAGEMENT)**

1. Tổng quan

* Là tính năng chính dùng để quản lý quan hệ đối tác.
* Tính năng có 3 giao diện xem chính bao gồm:
* Giao diện dạng thẻ: hiển thị các Đơn vị sở hữu đang có (được cấu hình trong tính năng Cài đặt thông số).
* Giao diện dạng danh sách: khi chọn một đơn vị sở hữu của người sử dụng, hệ thống sẽ hiển thị các trường thông tin thể
  hiện các bản ghi là các cơ hội của đơn vị sử dụng đã chọn.
* Giao diện dạng biểu mẫu: khi ấn nút tạo mới hoặc chọn một bản ghi bất kỳ, hệ thống sẽ hiển thị các trường thông tin
  chi tiết hơn và có thể chỉnh sửa. Với bản ghi tạo mới thì sau khi lưu, thông tin bản ghi sẽ hiển thị ở trên cùng danh
  sách trong giao diện dạng danh sách.

---

2. Các trường thông tin và một số tính năng trong PRM:

* Tên cơ hội: là trường thông tin trên cùng đầu tiên của giao diện biểu mẫu (mặc định khi tạo bản ghi sẽ hiển thị là Mới
  và không được sửa) thể hiện tên của một cơ hội với một đối tác, khi thực hiện lưu bản ghi sẽ tự sinh theo
  mẫu [Mã cơ hội]_Mã đối tác_Tên đối tác.
* Thanh trạng thái: là thông tin hiển thị góc trên cùng bên phải, thể hiện các mối quan hệ có phân loại là PRM và được
  sắp xếp theo thứ tự đã cấu hình.
* Mã cơ hội: hiển thị trong ngoặc vuông ở trường Tên cơ hội (không được sửa), thể hiện mã của một cơ hội với một đối
  tác, khi thực hiện lưu bản ghi sẽ tự sinh theo theo mẫu PRMxxxxx (ví dụ PRM00001) và tự tăng theo thứ tự của các bản
  ghi.
* Liên hệ lần cuối: thể hiện thời gian liên hệ với đối tác (không được sửa), mặc định khi tạo bản ghi sẽ là Hôm nay, nếu
  cơ hội đó đã trở thành đối tác POM thì Liên hệ lần cuối sẽ ẩn đi. Khi có sự thay đổi ở trường Trạng thái chi tiết,
  trong phần Ghi chú phía bên phải của giao diện và khi hoàn thành Lịch làm việc bất kỳ, Liên hệ lần cuối sẽ tự động cập
  nhật thời điểm hiện tại (tức Hôm nay).
* Điện thoại: là số điện thoại liên hệ của đối tác.
* [x] Trước khi lưu: khi nhập vào số điện thoại, hệ thống sẽ kiểm tra:
  Nếu số điện thoại là mới => cho phép lưu.
  Nếu số điện thoại là của một đối tác chưa được tạo cơ hội => tự động điền Tên và Email của đối tác.
  Nếu số điện thoại là của một đối tác đã được tạo cơ hội => không cho phép lưu và hiện ra thông báo trùng cơ hội với
  thông tin của đối tác bị trùng.
* [x] Sau khi lưu: không cho phép sửa trên giao diện của cơ hội, chỉ được phép sửa trên giao diện của Hồ sơ (thay đổi
  trong Hồ sơ sẽ tự cập nhật ở cơ hội).
* Email: là địa chỉ hòm thư liên hệ của đối tác.
* [x] Trước khi lưu: khi nhập vào Email, hệ thống sẽ kiểm tra:
  Nếu Email là mới => cho phép lưu.
  Nếu Email là của một đối tác chưa được tạo cơ hội => tự động điền Tên và số điện thoại của đối tác.
  Nếu Email là của một đối tác đã được tạo cơ hội => không cho phép lưu và hiện ra thông báo trùng cơ hội với thông tin
  của đối tác bị trùng.
* [x] Sau khi lưu: không cho phép sửa trên giao diện của cơ hội, chỉ được phép sửa trên giao diện của Hồ sơ (thay đổi
  trong Hồ sơ sẽ tự cập nhật ở cơ hội).
* Tên đối tác: tên của đối tác, có thể là tên cá nhân hoặc tên một tổ chức (bắt buộc nhập khi tạo bản ghi).
* Người phụ trách: tên của người phụ trách quản lý đối tác (mặc định là tên của người dùng khi tạo bản ghi và không được
  sửa).
* Đơn vị sở hữu: tên của đơn vị sở hữu (mặc định là đơn vị sở hữu người dùng chọn khi ở giao diện dạng thẻ và không được
  sửa).
* Nhóm đối tác: chọn một hoặc nhiều trong các nhóm đối tác đã cấu hình (bắt buộc nhập để tạo bản ghi).
* Nguồn đối tác: chọn một trong các nguồn đối tác đã cấu hình (bắt buộc nhập để tạo bản ghi).
* Sản phẩm hợp tác: chọn một hoặc nhiều trong các sản phẩm hợp tác đã cấu hình (bắt buộc nhập để chuyển trạng thái).
* Tình trạng gọi: chọn một trong các tình trạng gọi đã cấu hình (bắt buộc nhập để tạo bản ghi), lựa chọn tình trạng gọi
  sẽ chỉ hiển thị các tình trạng gọi liên kết với mối quan hệ được cấu hình.
* Trạng thái chi tiết: sẽ hiển thị sau khi chọn tình trạng gọi, chọn một trong các trạng thái chi tiết đã cấu hình theo
  tình trạng gọi bên trên (bắt buộc nhập để tạo bản ghi).
* Tỉnh/Thành phố: Tỉnh, thành phố của đối tác.
* Quận/Huyện: Quận, huyện của đối tác.
* Đơn vị công tác: đơn vị công tác của đối tác.
* Chính sách hợp tác: chọn một trong các chính sách hợp tác đã cấu hình.
* Trạng thái hợp đồng: bao gồm 3 trạng thái Không ký, Đang trình ký và Đã ký. Khi trạng thái hợp đồng là Đã ký sẽ hiện
  các trường thông tin gồm Số hợp đồng, Ngày ký hợp đồng và Hợp đồng để cho người dùng điền.
* Số hợp đồng: mã số hợp đồng với đối tác.
* Ngày ký hợp đồng: ngày ký hợp đồng với đối tác.
* Hợp đồng: chọn những tệp tin liên quan tới hợp đồng để đính kèm. Bắt buộc phải điền khi hợp đồng Đã ký và khi đối tác
  được nâng mối quan hệ Cần thông tin và giấy tờ.
* Giấy tờ cá nhân: chọn những tệp tin liên quan đến giấy tờ cá nhân để đính kèm.
* Bảng Mô tả: ghi chú hoặc mô tả thêm về cơ hội khi tạo.
* Bảng thông tin bổ sung: hiển thị thêm các trường thông tin về đối tác.
* [x] Người giới thiệu: chọn một người dùng hoặc một đối tác làm người giới thiệu.
* [x] Mã tiếp thị liên kết: tự động điền mã tiếp thị liên kết của người giới thiệu được chọn bên trên (không được sửa).
* [x] Ngày tạo: hiển thị thời gian tạo cơ hội (không được sửa).
* [x] Ngày lên level: hiển thị thời gian đối tác chuyển trạng thái sang mối quan hệ tương ứng đã được cấu hình (không
  được sửa).
* Chuyển POM: tính năng được tích hợp vào nút Chuyển POM hiển thị ở giao diện danh sách vào giao diện biểu mẫu, dùng để
  chuyển các đối tác PRM thành đối tác POM, sẽ chỉ hiển thị khi và chỉ khi đối tác bất kỳ ở trạng thái mối quan hệ cuối.
  Khi sử dụng hệ thống sẽ kiểm tra:
* [x] Nếu đối tác không có email hoặc số điện thoại => không cho phép chuyển POM và đưa ra thông báo yêu cầu điền bổ
  sung đầy đủ.
* [x] Nếu đối tác có đầy đủ email và số điện thoại => cho phép chuyển POM.
* Tạo tài khoản Affiliate: tính năng được tích hợp vào nút Tạo tài khoản Affiliate hiển thị trong giao diện biểu mẫu,
  dùng để tạo tài khoản Affiliate cho đối tác PRM, sẽ hiển thị khi và chỉ khi đối tác bất kỳ ở trạng thái mối quan hệ
  cuối. Khi sử dụng hệ thống sẽ kiểm tra:
* [x] Nếu đối tác không có email => không cho phép tạo tài khoản và đưa ra thông báo yêu cầu điền bổ sung email.
* [x] Nếu đối tác có email => cho phép tạo tài khoản.
* Giao cơ hội: cho phép người dùng chuyển giao một hoặc nhiều cơ hội cho một đội nhóm hoặc một cá nhân cụ thể:
* [x] Sau khi giao cơ hội cho cá nhân (chỉ hiển thị những người dùng được phân quyền sử dụng module PRM) thì cơ hội được
  chọn sẽ thay thế Người phụ trách hiện tại sang Người phụ trách mới mà người dùng chọn (bản chất là thay đổi Người phụ
  trách).
* [x] Sau khi giao cơ hội cho một đội nhóm thì các cơ hội sẽ được chia đều cho các thành viên trong đội nhóm đấy làm
  Người phụ trách.
* Thu hồi về kho: dùng để chuyển một hoặc nhiều cơ hội mà người sử dụng cảm thấy hết tiềm năng hoặc ngừng chăm sóc về
  kho lưu trữ PRM.

---

3. Phân quyền

* Với nhóm người dùng là Nhân viên:
* [x] Cho phép xem và sửa các cơ hội của bản thân Nhân viên đấy là Người phụ trách.
* [x] Cho phép giao các cơ hội của bản thân Nhân viên cho một đội nhóm hoặc một cá nhân khác.
* [x] Không cho phép xem và sửa các cơ hội của Nhân viên khác.
* [x] Không cho phép xóa và thu hồi các cơ hội về kho.
* Với nhóm người dùng là Quản lý:
* [x] Cho phép xem và sửa các cơ hội của bản thân là Người phụ trách và các cơ hội của các thành viên trong đội nhóm do
  bản thân Quản lý đấy quản lý (thiết lập trong cấu hình Đội nhóm).
* [x] Cho phép giao cơ hội của bản thân Quản lý và các cơ hội của các thành viên trong đội nhóm do bản thân Quản lý đấy
  quản lý cho một đội nhóm hoặc một cá nhân khác.
* [x] Không cho phép xóa và thu hồi các cơ hội về kho.
* Với nhóm người dùng là Trưởng phòng:
* [x] Cho phép xem và sửa tất cả cơ hội của tất cả mọi Người phụ trách và các cơ hội không có Người phụ trách.
* [x] Cho phép giao cơ hội của tất cả mọi người cho một đội nhóm hoặc một cá nhân khác.
* [x] Không cho phép xóa và thu hồi các cơ hội về kho.
* Với nhóm người dùng là Quản trị viên: là nhóm quyền cao nhất, có toàn bộ quyền của Trưởng phòng. Ngoài ra còn có quyền
  xóa và thu hồi các cơ hội về kho.

# **IV. POM (PARTNER OPERATING MANAGEMENT)**

1. Tổng quan

* Là tính năng chính dùng để quản lý điều hành đối tác (những đối tác PRM đã trở thành đối tác chính của người sử dụng).
* Tương tự như PRM, POM cũng có 3 giao diện xem chính bao gồm: dạng thẻ, dạng danh sách và dạng biểu mẫu.
* POM không cho phép tạo bản ghi như là tạo các cơ hội bên PRM vì phải có các cơ hội bên PRM thì mới có các đối tác POM.

---

2. Các trường thông tin và một số tính năng trong POM:

* Về cơ bản, các trường thông tin trong POM đều giống các trường trong PRM chỉ có một chút sự thay đổi nhằm phân biệt
  đối tác PRM với đối tác POM.
* Tên cơ hội: là trường thông tin trên cùng đầu tiên của giao diện biểu mẫu (không được sửa) thể hiện tên của một cơ hội
  tiếp tục hợp tác với một đối tác, khi thực hiện Chuyển POM từ tính năng PRM sẽ tự sinh theo mẫu [POMxxxxxx]-{Tên Khách
  hàng} với xxxxxx là số có 6 chữ số tịnh tiến tăng dần và tự tăng theo thứ tự của các bản ghi.
* Lý do ngừng hợp tác: sẽ hiển thị sau khi chọn Trạng thái chi tiết là “Ngừng hợp tác”, chọn một trong các lý do ngừng
  hợp tác đã cấu hình (bắt buộc nhập để tạo bản ghi).
* Chi tiết lý do ngừng hợp tác: sẽ hiển thị sau khi chọn Lý do ngừng hợp tác, chọn một trong các chi tiết đã cấu hình
  theo Lý do ngừng hợp tác bên trên (bắt buộc nhập để tạo bản ghi).
* Thanh trạng thái: là thông tin hiển thị góc trên cùng bên phải, thể hiện các mối quan hệ có phân loại là POM và được
  sắp xếp theo thứ tự đã cấu hình.
* Bảng thông tin bổ sung: hiển thị thêm các trường thông tin về đối tác, ngoài những trường như bên PRM, POM có thêm
  những trường thông tin sau
* [x] Cấp đại lý: chọn một trong các cấp độ làm cấp của đại lý.
* [x] Trạng thái đối tác: chọn một trong các trạng thái làm trạng thái hiện tại của đối tác.
* [x] Trạng thái tạm dừng: sẽ xuất hiện sau khi chọn trạng thái đối tác là Tạm dừng, chọn một trong các trạng thái làm
  trạng thái tạm dừng của đối tác.
* [x] Thời gian ngừng hợp tác: hiển thị thời gian đối tác chuyển trạng thái sang Ngừng hợp tác (không được sửa).

---

3. Phân quyền

* Phân quyền của POM cũng tương tự như PRM.

# **V. KHO LƯU TRỮ**

* Chỉ Quản lý, Trưởng phòng và Quản trị viên mới có thể thấy và truy cập được vào Kho lưu trữ.
* Dùng để lưu trữ một hoặc nhiều cơ hội PRM hay một hoặc nhiều đối tác POM mà người sử dụng cảm thấy hết tiềm năng hoặc
  ngừng chăm sóc.
* Tái sử dụng: chọn một hoặc nhiều cơ hội để tái sử dụng (chỉ Quản trị viên mới được quyền tái sử dụng), về bản chất khi
  thu hồi về kho là cơ hội hoặc đối tác đó không có tiềm năng nên khi tái sử dụng thì cơ hội hoặc đối tác đó sẽ trở
  thành có tiềm năng. Sau khi tái sử dụng, cơ hội hoặc đối tác được tái sử dụng sẽ có thêm trường Tên nguồn tái sử dụng
  để nhận biết cơ hội hoặc đối tác này đã được tái sử dụng.

# **VI. LÀM SẠCH DỮ LIỆU IMPORT PRM**

* Tính năng cho phép tải lên file excel chứa các số điện thoại của các liên hệ.
* Sau khi làm sạch sẽ trả ra một file excel chứa các số điện thoại trùng (số điện thoại của các liên hệ đã tồn tại trên
  hệ thống.

```AnhHQ_AUM_ITC```
