from odoo.models import BaseModel
from odoo.exceptions import ValidationError, UserError
from odoo import fields, models, api, _

class ThStatusCategoryAFF(BaseModel):
    _inherit = 'th.status.category'

    def th_create_status_category(self, datas):
        if self.search_count([('name', '=', datas.name), ('th_type', '=', datas.th_type)]) > 0:
            raise ValidationError("Nhóm trạng thái %s đã tồn tại!" % self.name)
        else:
            status_category = super(ThStatusCategoryAFF, self).create(datas.dict())
        return status_category


class ThStatusDetailAFF(BaseModel):
    _inherit = 'th.status.detail'

    def th_create_status_detail(self, datas):
        if self.search_count([('name', '=', datas.name)]) > 0:
            raise ValidationError("Trạng thái chi tiết %s đã tồn tại!" % self.name)
        else:
            status_detail = super(ThStatusDetailAFF, self).create(datas.dict())
        return status_detail


class ThOwnershipUnitAFF(BaseModel):
    _inherit = 'th.ownership.unit'

    def th_create_ownership_unit(self, datas):
        if self.search_count([('name', '=', datas.name),('th_code', '=', datas.th_code)]) > 0:
            raise ValidationError("Đơn vị sở hữu %s đã tồn tại!" % self.name)
        else:
            status_detail = super(ThOwnershipUnitAFF, self).create(datas.dict())
        return status_detail

class ThSourceGroupAFF(BaseModel):
    _inherit = 'th.source.group'

    def th_create_source_group(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Nhóm nguồn %s đã tồn tại!" % name)
        else:
            status_detail = super(ThSourceGroupAFF, self).create(datas)
        return status_detail

class ThGraduationSystemAFF(models.Model):
    _inherit = "th.graduation.system"

    def th_create_graduation_system(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Hệ tốt nghiệp %s đã tồn tại!" % name)
        else:
            status_detail = super(ThGraduationSystemAFF, self).create(datas)
        return status_detail

class ThMajor(models.Model):
    _inherit = "th.major"

    def th_create_major(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Ngành học %s đã tồn tại!" % name)
        else:
            major = super(ThMajor, self).create(datas)
        return major

class ThAdmissionsStation(models.Model):
    _inherit = "th.admissions.station"

    def th_create_admissions_station(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Trạm tuyển sinh %s đã tồn tại!" % name)
        else:
            admissions_station = super(ThAdmissionsStation, self).create(datas)
        return admissions_station

class ThAdmissionsRegion(models.Model):
    _inherit = "th.admissions.region"

    def th_create_admissions_region(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Vùng tuyển sinh %s đã tồn tại!" % name)
        else:
            admissions_region = super(ThAdmissionsRegion, self).create(datas)
        return admissions_region

class ThOriginAFF(BaseModel):
    _inherit = 'th.origin'

    def th_create_origin(self, datas):
        if self.search_count([('name', '=', datas.get('name', False)),('th_code', '=', datas.get('th_code', False))]) > 0:
            raise ValidationError("Xuất xứ %s đã tồn tại!" % self.name)
        else:
            # datas.th_module_ids = eval(datas.th_module_ids) if datas.th_module_ids else False
            # datas.th_university_major_ids = eval(datas.th_university_major_ids) if datas.th_university_major_ids else False
            status_detail = super(ThOriginAFF, self).create(datas)
        return status_detail


class ThUniversityMajorAFF(BaseModel):
    _inherit = "th.university.major"

    def th_create_university_major(self, datas):
        university_major = super(ThUniversityMajorAFF, self).create(datas)
        return university_major

class ThInfoChannel(models.Model):
    _inherit = "th.info.channel"
    th_channel_samp_id = fields.Integer(string="")
    def th_create_channel(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Kênh %s đã tồn tại!" % name)
        else:
            channel = super(ThInfoChannel, self).create(datas)
        return channel


class ThCountryDistrict(models.Model):
    _inherit = "th.country.district"
    def th_create_district(self, datas):
        th_ref = datas.get('th_ref')
        th_state_id = datas.get('th_state_id')
        country_state = self.env['res.country.state'].search([('id', '=', th_state_id)])
        if not country_state:
            raise ValidationError("Mã Tỉnh/Thành (th_state_id=%s) không tồn tại!" % th_state_id)
        if self.search_count([('th_ref', '=', th_ref)]) > 0:
            raise ValidationError("Mã Quận/Huyện %s đã tồn tại!" % datas.get('name'))
        if country_state:
            data = {'name': datas.get('name'),
                    'th_ref': th_ref,
                    'th_state_id': country_state.id}
            district = super(ThCountryDistrict, self).create(data)
        return district

class ThCountryWard(models.Model):
    _inherit = "th.country.ward"
    def th_create_ward(self, datas):
        th_ref = datas.get('th_ref')
        th_district_id = datas.get('th_district_id')
        district = self.env['th.country.district'].search([('id', '=', th_district_id)])
        if self.search_count([('th_ref', '=', th_ref)]) > 0:
            raise ValidationError("Mã phường/xã %s đã tồn tại!" % datas.get('name'))
        if not district:
            raise ValidationError("Mã Quận/Huyện (th_state_id=%s) không tồn tại!" % th_district_id)
        else:
            data = {'th_c_ward_samp_id': datas.get('th_c_ward_samp_id'),
                    'name': datas.get('name'),
                    'th_ref': th_ref,
                    'th_district_id': district.id}
            Ward = super(ThCountryWard, self).create(data)
        return Ward

class ThCountryState(models.Model):
    _inherit = "res.country.state"
    def th_create_state(self, datas):
        code = datas.get('code')
        country_id = datas.get('country_id')
        state = self.env['res.country'].search([('code', '=', country_id)])
        if self.search_count([('code', '=', code)]) > 0:
            raise ValidationError("Mã tỉnh %s đã tồn tại!" % datas.get('name'))
        if not state:
            raise ValidationError("Mã Quốc gia tồn tại!")
        else:
            data = {'name': datas.get('name'),
                    'code': code,
                    'country_id': state.id}
            state = super(ThCountryState, self).create(data)
        return state

class ThTrainingSystem(models.Model):
    _inherit = "th.training.system"
    def th_create_training_system(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Hệ đào tạo %s đã tồn tại!" % name)
        else:
            training_system = super(ThTrainingSystem, self).create(datas)
        return training_system

class ThCrmTag(models.Model):
    _inherit = "crm.tag"
    def th_create_crm_tag(self, datas):
        name = datas.get('name')
        if self.search_count([('name', '=', name)]) > 0:
            raise ValidationError("Nhóm cơ hội %s đã tồn tại!" % name)
        else:
            crm_tag = super(ThCrmTag, self).create(datas)
        return crm_tag