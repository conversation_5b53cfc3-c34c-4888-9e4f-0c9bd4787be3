from odoo import fields, models, api




class ThStudentProfile(models.Model):
    _inherit = "th.student.profile"

    th_student_profile_samb_id = fields.Integer(string='Id hồ sơ samp', copy=False)
    th_calling_status_id = fields.Char(string="Tình trạng gọi hồ sơ",tracking=True)
    th_missing_file = fields.Char(string="HS.Thu Thiếu",tracking=True)
    th_12_performance = fields.Char(string="Học lực lớp 12",tracking=True)
    th_12_conduct = fields.Char('Hạnh kiểm lớp 12', tracking=True)
    th_lead_id = fields.Many2one("crm.lead", string="C<PERSON> hội")

    def th_create_student_profile(self, datas):
        th_lead_id = datas.get('th_lead_id',False)
        lead = self.env['crm.lead'].search([('id', '=', th_lead_id)])
        datas['th_partner_id'] = lead.partner_id.id
        student_profile = super(ThStudentProfile, self).create(datas)
        lead.th_student_profile_id = student_profile.id
        return student_profile
