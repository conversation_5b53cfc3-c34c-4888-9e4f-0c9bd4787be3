from odoo.models import BaseModel
from odoo.exceptions import ValidationError, UserError
from odoo import fields, models, api, _


class InternshipConditions(BaseModel):
    _inherit = 'th.internship.conditions'

    def th_create_internship_conditions(self, datas):
        internship_conditions = super(InternshipConditions, self).create(datas.dict())
        return internship_conditions


class ExemptedSubject(BaseModel):
    _inherit = 'th.exempted.subject'

    def th_create_exempted_subject(self, datas):
        exempted_subject = super(ExemptedSubject, self).create(datas.dict())
        return exempted_subject


class SubjectRegistrationStatus(BaseModel):
    _inherit = 'th.subject.registration.status'

    def th_create_subject_registration_status(self, datas):
        subject_registration_status = super(SubjectRegistrationStatus, self).create(datas.dict())
        return subject_registration_status


class StudentStatusDetail(BaseModel):
    _inherit = 'th.student.status.detail'

    def th_create_student_status_detail(self, datas):
        student_status_detail = super(StudentStatusDetail, self).create(datas.dict())
        return student_status_detail


class StatusStudentParticular(models.Model):
    _inherit = "th.status.student.particular"
    def th_create_status_student_particular(self, data):
        rec = self.env['th.status.student.particular'].sudo().create(dict(data))
        return rec

class StatusStudentParticular(models.Model):
    _inherit = "th.fee.status.particular"
    def th_create_fee_status(self, data):
        rec = self.env['th.fee.status.particular'].sudo().create(dict(data))
        return rec

class StudentStatus(BaseModel):
    _inherit = 'th.student.status'

    def th_create_student_status(self, datas):
        # datas.th_status_detail_ids = eval(datas.th_status_detail_ids) if datas.th_status_detail_ids else False
        # datas.note = datas.note if datas.note else eval(datas.note)
        student_status = super(StudentStatus, self).create(datas.dict())
        return student_status
