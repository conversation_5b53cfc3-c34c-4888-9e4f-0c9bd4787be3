<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2022 ACSONE SA/NV
     License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL). -->
<odoo>

    <record model="ir.ui.view" id="th_fastapi_endpoint_cord_form_view">
        <field name="name">fastapi.endpoint.demo.form (in fastapi)</field>
        <field name="model">fastapi.endpoint</field>
        <field name="inherit_id" ref="fastapi.fastapi_endpoint_form_view"/>
        <field name="arch" type="xml">

            <xpath expr="//sheet" position="inside">
                <span name="configuration" position="after">
                    <group name="demo_app_configuration" title="Configuration">
                        <group>
                            <field name="th_auth_method" attrs="{'invisible': [('app', 'not in', ['curd', 'user'])], 'required': [('app', 'in', ['curd', 'user'])]}" force_save="1"/>
                            <field name="th_api_key" attrs="{'required': [('th_auth_method', '!=', False)]}" force_save="1"/>
                        </group>
                    </group>
                </span>
                <notebook>
                    <page string="Chấp thuận truy cập">
                        <field name="th_access_ids">
                            <tree editable="top">
                                <field name="name"/>
                                <field name="th_url"/>
                                <field name="th_is_access"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </xpath>



        </field>
    </record>

</odoo>
