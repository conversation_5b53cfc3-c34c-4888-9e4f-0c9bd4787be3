<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_code_name" model="ir.sequence">
        <field name="name">Name Code</field>
        <field name="code">prm.code</field>
        <field name="prefix">P</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"></field>
    </record>

    <record id="th_pom_code_name" model="ir.sequence">
        <field name="name">POM Name Code</field>
        <field name="code">pom.code</field>
        <field name="prefix">F</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"></field>
    </record>
</odoo>