from odoo import fields, models, api


class ThApm(models.Model):
    _inherit = "th.apm"

    def th_create_th_apm(self, datas):
        product = datas.get('th_product_ids', False)
        product_product = self.env['product.product'].search([('product_tmpl_id', '=', product)])
        datas['th_product_ids'] = product_product.ids
        # th_apm_contact_trait_ids = False
        # if datas.get('th_apm_contact_trait_ids', False):
        # th_apm_contact_trait_ids = datas.pop("th_apm_contact_trait_ids", None)
        apm_lead = super(ThApm, self).with_context(th_sync=True).create(datas)
        # if th_apm_contact_trait_ids and apm_lead:
        #     for rec in th_apm_contact_trait_ids:
        #         apm_trait_value = self.env['th.apm.contact.trait'].with_context(default_th_partner_id=apm_lead.th_partner_id.id).create(rec)
        return apm_lead
    def th_write_th_apm(self, datas):
        product = datas.get('th_product_ids', False)
        product_product = self.env['product.product'].search([('product_tmpl_id', '=', product)])
        datas['th_product_ids'] = product_product.ids
        # th_apm_contact_trait_ids = False
        # if datas.get('th_apm_contact_trait_ids', False):
        #     th_apm_contact_trait_ids = datas.pop("th_apm_contact_trait_ids", None)
        apm_lead = super(ThApm, self).with_context(th_sync=True).write(datas)
        # if th_apm_contact_trait_ids and apm_lead:
        #     self.th_apm_contact_trait_ids.unlink()
        #     for rec in th_apm_contact_trait_ids:
        #         apm_trait_value = self.env['th.apm.contact.trait'].with_context(default_th_partner_id=self.th_partner_id.id).create(rec)
        return apm_lead

class ApmTeam(models.Model):
    _inherit = "th.apm.team"
    def th_create_apm_team(self, datas):
        rec = self.env['th.apm.team'].sudo().create(dict(datas))
        return rec