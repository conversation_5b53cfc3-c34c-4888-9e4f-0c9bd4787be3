# -*- coding: utf-8 -*-
from odoo import models, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class FastapiEndpointSetup(models.TransientModel):
    _name = 'fastapi.endpoint.setup'
    _description = 'FastAPI Endpoint Setup Helper'

    @api.model
    def setup_sc_api_endpoint(self):
        """
        Tự động tạo hoặc cập nhật FastAPI endpoint cho SC API v1
        """
        try:
            # Tìm endpoint hiện có
            endpoint = self.env['fastapi.endpoint'].search([
                ('root_path', '=', '/sc'),
                ('app', '=', 'v1')
            ], limit=1)
            
            if not endpoint:
                # Tạo endpoint mới
                endpoint = self.env['fastapi.endpoint'].create({
                    'name': 'SC API v1',
                    'root_path': '/sc',
                    'app': 'v1',
                    'th_auth_method': 'api_key',
                    'th_api_key': 'default_api_key_change_me',
                })
                _logger.info("Created new FastAPI endpoint: %s", endpoint.name)
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'message': _('FastAPI endpoint đã được tạo thành công. Vui lòng cập nhật API key trong cấu hình.'),
                        'type': 'success',
                        'sticky': True,
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'message': _('FastAPI endpoint đã tồn tại: %s') % endpoint.name,
                        'type': 'info',
                        'sticky': False,
                    }
                }
                
        except Exception as e:
            _logger.error("Error setting up FastAPI endpoint: %s", str(e))
            raise UserError(_('Lỗi khi tạo FastAPI endpoint: %s') % str(e))

    @api.model
    def check_api_configuration(self):
        """
        Kiểm tra cấu hình API và đưa ra khuyến nghị
        """
        issues = []
        
        # Kiểm tra FastAPI endpoint
        endpoint = self.env['fastapi.endpoint'].search([
            ('root_path', '=', '/sc'),
            ('app', '=', 'v1')
        ], limit=1)
        
        if not endpoint:
            issues.append("Không tìm thấy FastAPI endpoint với root_path='/sc' và app='v1'")
        else:
            if not endpoint.th_api_key or endpoint.th_api_key == 'default_api_key_change_me':
                issues.append("API key trong FastAPI endpoint chưa được cấu hình đúng")
        
        # Kiểm tra API server
        api_server = self.env['th.api.server'].search([
            ('th_type', '=', 'aff'),
            ('state', '=', 'deploy')
        ], limit=1)
        
        if not api_server:
            issues.append("Không tìm thấy API server với loại 'aff' ở trạng thái 'deploy'")
        else:
            if not api_server.th_api_key:
                issues.append("API key trong API server chưa được cấu hình")
            elif endpoint and api_server.th_api_key != endpoint.th_api_key:
                issues.append("API key trong FastAPI endpoint và API server không khớp")
        
        if issues:
            message = "Các vấn đề cấu hình API:\n" + "\n".join(f"- {issue}" for issue in issues)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': message,
                    'type': 'warning',
                    'sticky': True,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': _('Cấu hình API đã được thiết lập đúng!'),
                    'type': 'success',
                    'sticky': False,
                }
            }
