# Hướng dẫn cấu hình API FastAPI

## Vấn đề đã sửa

Lỗi 404 NOT FOUND khi gọi API `/sc/api/v1/users` đã được sửa bằng cách:

1. **Cập nhật endpoint router**: Thay đổi từ `/api/users` thành `/api/v1/users` trong `res_user_routers.py`
2. **Tạo record FastAPI endpoint**: Tạo record với `root_path = '/sc'` và `app = 'v1'`
3. **Sửa lỗi logic trong method `th_create_user`**: <PERSON><PERSON>m bảo return đúng object

## Cấu hình cần thiết

### 1. Cấu hình FastAPI Endpoint

Sau khi cài đặt module, cần cấu hình record `fastapi.endpoint`:

1. Vào **Settings > Technical > FastAPI > Endpoints**
2. Tìm record "SC API v1" hoặc tạo mới với:
   - **Name**: SC API v1
   - **Root Path**: /sc
   - **App**: SAMP V1
   - **Auth Method**: Api Key
   - **API Key**: [Điền API key của bạn]

### 2. Cấu hình API Server

Trong module `th_base`, cấu hình record `th.api.server`:

1. Vào menu API Server
2. Tạo hoặc cập nhật record với:
   - **URL server**: http://localhost:8099 (hoặc URL server của bạn)
   - **API Key**: [Cùng API key như trong FastAPI Endpoint]
   - **API root**: /sc
   - **Loại**: aff (affiliate)
   - **Trạng thái**: Triển khai

### 3. URL API hoàn chỉnh

Với cấu hình trên, URL API sẽ là:
```
http://localhost:8099/sc/api/v1/users
```

Trong đó:
- `http://localhost:8099`: Base URL từ `th_url_api`
- `/sc`: Root path của FastAPI endpoint
- `/api/v1/users`: Endpoint được định nghĩa trong router

## Kiểm tra cấu hình

1. Đảm bảo API key trong `fastapi.endpoint` và `th.api.server` phải giống nhau
2. Kiểm tra `root_path` trong `fastapi.endpoint` phải là `/sc`
3. Kiểm tra `app` trong `fastapi.endpoint` phải là `v1` hoặc `user`
4. Đảm bảo server FastAPI đang chạy trên port 8099

## Test API

Có thể test API bằng cách:

```bash
curl -X POST "http://localhost:8099/sc/api/v1/users" \
  -H "api-key: your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "login": "<EMAIL>"
  }'
```
