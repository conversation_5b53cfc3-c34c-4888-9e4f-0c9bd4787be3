<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Server Actions for FastAPI Setup -->
    <record id="action_setup_sc_api_endpoint" model="ir.actions.server">
        <field name="name">Thi<PERSON><PERSON> lập SC API Endpoint</field>
        <field name="model_id" ref="model_fastapi_endpoint_setup"/>
        <field name="state">code</field>
        <field name="code">
            action = model.setup_sc_api_endpoint()
        </field>
    </record>

    <record id="action_check_api_configuration" model="ir.actions.server">
        <field name="name">Ki<PERSON>m tra cấu hình API</field>
        <field name="model_id" ref="model_fastapi_endpoint_setup"/>
        <field name="state">code</field>
        <field name="code">
            action = model.check_api_configuration()
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_fastapi_setup"
              name="API Setup"
              parent="fastapi.fastapi_menu"
              sequence="100"/>

    <menuitem id="menu_setup_sc_api_endpoint"
              name="Thiết lập SC API Endpoint"
              parent="menu_fastapi_setup"
              action="action_setup_sc_api_endpoint"
              sequence="10"/>

    <menuitem id="menu_check_api_configuration"
              name="Kiểm tra cấu hình API"
              parent="menu_fastapi_setup"
              action="action_check_api_configuration"
              sequence="20"/>
</odoo>
