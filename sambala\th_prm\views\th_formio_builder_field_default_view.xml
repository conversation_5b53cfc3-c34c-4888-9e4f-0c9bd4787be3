<odoo>
<!--    <record id="view_th_formio_builder_field_default_tree_inherit" model="ir.ui.view">-->
<!--        <field name="name">th.formio.builder.field.default.tree.inherit</field>-->
<!--        <field name="model">th.formio.builder.field.default</field>-->
<!--        <field name="inherit_id" ref="th_setup_parameters.th_formio_view_tree"/>-->
<!--        <field name="arch" type="xml">-->
<!--            <xpath expr="//field[@name='th_flag']" position="after">-->
<!--                <field name="th_partner_group_id"/>-->
<!--                <field name="th_partner_source_id"/>-->
<!--            </xpath>-->
<!--        </field>-->
<!--    </record>-->

<record id="view_th_formio_builder_field_default_prm_form_inherit" model="ir.ui.view">
        <field name="name">th.formio.builder.field.default.form.inherit</field>
        <field name="model">th.formio.builder.field.aff.default</field>
        <field name="inherit_id" ref="th_setup_parameters.th_formio_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group_right']" position="inside">
                <field name="th_partner_group_id" attrs="{'invisible': [('th_module', '!=', 'prm')]}"/>
                <field name="th_partner_source_id" attrs="{'invisible': [('th_module', '!=', 'prm')]}"/>
            </xpath>
        </field>
    </record>
     </odoo>