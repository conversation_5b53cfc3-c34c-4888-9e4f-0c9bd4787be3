from typing import Annotated
from odoo.http import request
from ..schemas import UserDatas,UserResponse
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError, ValidationError

router = APIRouter(tags=["user"])


def write_log(message: str):
    print(message)


@router.post("/api/users", response_model=UserResponse)
def create_user(
    user_data: UserDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            user = fastapi.env['res.users'].th_create_user(datas=user_data)
            return UserResponse(id=user.id, email=user.email, name=user.name)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
