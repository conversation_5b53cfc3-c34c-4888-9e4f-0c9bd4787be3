<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    <template id="th_template_send_update_info">-->
    <!--        <div>-->
    <!--            <div>Dear<t t-esc="manager"/>,-->
    <!--            </div>-->
    <!--            <t t-esc="partner"/>-->
    <!--            <t t-esc="message"/>-->
    <!--        </div>-->
    <!--        <div style="margin: 16px 0px 16px 0px;">-->
    <!--            <a t-att-href="access_link" t-esc="update_info"-->
    <!--               style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;">-->
    <!--            </a>-->
    <!--        </div>-->
    <!--    </template>-->
    <data>
        <record id="th_template_send_update_info" model="mail.template">
            <field name="name"><PERSON><PERSON><PERSON> cầu cập nhật thông tin</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="subject">{{ ctx.get('subject') }}</field>
            <field name="email_from">{{ ctx.get('email_from') }}</field>
            <field name="email_to">{{ ctx.get('email_to') }}</field>
            <field name="auto_delete" eval="False"/>
            <field name="body_html" type="html">
                <div>
                    <div>Dear <t t-esc="ctx.get('manager')"/>,
                    </div>
                    <div>
                        <t t-esc="ctx.get('partner')"/>
                        <t t-esc="ctx.get('message')"/>
                    </div>
                </div>
                <div style="margin: 16px 0px 16px 0px;">
                    <a t-esc="ctx.get('update_info')"
                       style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;">
                    </a>
                </div>
                <div>
                    <a>Thanks you,</a>
                </div>
            </field>
        </record>
    </data>
</odoo>